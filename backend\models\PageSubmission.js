const mongoose = require('mongoose');

const pageSubmissionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  imageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AdminImage',
    required: true
  },
  submissionContent: {
    type: String,
    required: function() {
      return !this.isDraft; // Only required if not a draft
    }
  },
  isDraft: {
    type: Boolean,
    default: false
  },
  lastSaved: {
    type: Date,
    default: Date.now
  },
  submissionTimestamp: {
    type: Date
  },
  isReviewed: {
    type: <PERSON>olean,
    default: false
  },
  reviewScore: {
    type: Number,
    min: 0,
    max: 100
  },
  reviewComments: {
    type: String
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: {
    type: Date
  },
  status: {
    type: String,
    enum: ['draft', 'submitted', 'under_review', 'approved', 'rejected'],
    default: function() {
      return this.isDraft ? 'draft' : 'submitted';
    }
  }
});

// Create compound index for efficient queries
pageSubmissionSchema.index({ userId: 1, imageId: 1 });
pageSubmissionSchema.index({ userId: 1, imageId: 1, isDraft: 1 }); // For finding drafts
pageSubmissionSchema.index({ submissionTimestamp: -1 });
pageSubmissionSchema.index({ lastSaved: -1 });
pageSubmissionSchema.index({ status: 1 });
pageSubmissionSchema.index({ isDraft: 1 });

module.exports = mongoose.model('PageSubmission', pageSubmissionSchema);
