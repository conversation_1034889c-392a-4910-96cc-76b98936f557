{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedAdmin.js", "test": "jest --testTimeout=30000", "test:watch": "jest --watch --testTimeout=30000", "test:coverage": "jest --coverage --testTimeout=30000"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "html-pdf-node": "^1.0.8", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "mongoose": "^8.15.1", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^4.2.0", "nodemailer": "^7.0.3", "prop-types": "^15.8.1", "puppeteer": "^24.10.0", "socket.io": "^4.8.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["controllers/**/*.js", "services/**/*.js", "middleware/**/*.js"], "coverageDirectory": "coverage", "testMatch": ["**/tests/**/*.test.js"]}}